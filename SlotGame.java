import java.util.Random;
import java.util.Scanner;

/**
 * Slot machine game with 3 reels and various winning combinations.
 */
public class SlotGame {
    private int playerBalance;
    private int currentBet;
    private int lastBet; // Store the last bet for quick replay
    private Scanner scanner;
    private Random random;
    
    // Slot symbols with better visual representation
    private static final String[] SYMBOLS = {"<7>", "BAR", "CHR", "LMN", "BEL", "***"};
    private static final String JACKPOT_SYMBOL = "<7>";
    
    public SlotGame(int startingBalance) {
        this.playerBalance = startingBalance;
        this.currentBet = 0;
        this.lastBet = 10; // Default bet amount
        this.scanner = new Scanner(System.in);
        this.random = new Random();
    }
    
    /**
     * Allow player to place a bet.
     * @param useLastBet Whether to use the last bet amount automatically
     * @return true if bet was placed successfully
     */
    private boolean placeBet(boolean useLastBet) {
        System.out.println("\n$ Your balance: $" + playerBalance);

        if (playerBalance <= 0) {
            System.out.println("X You're out of money! Game over.");
            return false;
        }

        if (useLastBet && lastBet > 0 && lastBet <= playerBalance) {
            currentBet = lastBet;
            System.out.println("* Using previous bet: $" + currentBet);
            return true;
        }

        while (true) {
            try {
                System.out.print("$ Place your bet (1-" + Math.min(playerBalance, 100) + "): $");
                int bet = scanner.nextInt();
                scanner.nextLine(); // Consume newline

                if (bet >= 1 && bet <= playerBalance && bet <= 100) {
                    currentBet = bet;
                    lastBet = bet; // Store for next time
                    System.out.println("+ Bet placed: $" + bet);
                    return true;
                } else if (bet > 100) {
                    System.out.println("- Maximum bet is $100");
                } else {
                    System.out.println("- Please bet between $1 and $" + Math.min(playerBalance, 100));
                }
            } catch (Exception e) {
                System.out.println("- Please enter a valid number.");
                scanner.nextLine(); // Clear invalid input
            }
        }
    }

    /**
     * Allow player to change their bet amount.
     * @return true if bet was changed successfully
     */
    private boolean changeBet() {
        System.out.println("\n* CHANGE BET AMOUNT");
        System.out.println("Current bet: $" + lastBet);
        return placeBet(false); // Force new bet entry
    }
    
    /**
     * Spin the slot machine reels.
     * @return Array of 3 symbols
     */
    private String[] spinReels() {
        String[] result = new String[3];
        for (int i = 0; i < 3; i++) {
            result[i] = SYMBOLS[random.nextInt(SYMBOLS.length)];
        }
        return result;
    }
    
    /**
     * Display the slot machine with spinning animation.
     * @param finalResult The final result to display
     */
    private void displaySlotMachine(String[] finalResult) {
        System.out.println("\n" + repeatString("=", 50));
        System.out.println("*        SLOT MACHINE SPINNING...        *");
        System.out.println(repeatString("=", 50));
        System.out.println("* Bet: $" + currentBet + "                                 *");
        System.out.println(repeatString("-", 50));

        // Spinning animation
        for (int spin = 0; spin < 8; spin++) {
            String[] tempResult = spinReels();
            System.out.print("\r* | " + tempResult[0] + " | " + tempResult[1] + " | " + tempResult[2] + " | *");
            try {
                Thread.sleep(150);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }

        // Final result
        System.out.print("\r* | " + finalResult[0] + " | " + finalResult[1] + " | " + finalResult[2] + " | *");
        System.out.println("\n" + repeatString("=", 50));
    }
    
    /**
     * Calculate winnings based on the slot result.
     * @param result The slot machine result
     * @return Winnings multiplier
     */
    private int calculateWinnings(String[] result) {
        // Check for three of a kind
        if (result[0].equals(result[1]) && result[1].equals(result[2])) {
            if (result[0].equals(JACKPOT_SYMBOL)) {
                return 50; // Jackpot: 50x bet
            } else if (result[0].equals("BAR")) {
                return 20; // Triple BAR: 20x bet
            } else if (result[0].equals("BEL")) {
                return 15; // Triple BELL: 15x bet
            } else if (result[0].equals("***")) {
                return 10; // Triple STAR: 10x bet
            } else if (result[0].equals("CHR")) {
                return 8; // Triple CHERRY: 8x bet
            } else if (result[0].equals("LMN")) {
                return 5; // Triple LEMON: 5x bet
            }
        }
        
        // Check for two of a kind
        if (result[0].equals(result[1]) || result[1].equals(result[2]) || result[0].equals(result[2])) {
            if (containsSymbol(result, JACKPOT_SYMBOL)) {
                return 5; // Two 7s: 5x bet
            } else if (containsSymbol(result, "BAR")) {
                return 3; // Two BARs: 3x bet
            } else if (containsSymbol(result, "CHR")) {
                return 2; // Two CHERRYs: 2x bet
            }
        }

        // Check for any CHERRY (consolation prize)
        if (containsSymbol(result, "CHR")) {
            return 1; // Any CHERRY: 1x bet (break even)
        }
        
        return 0; // No win
    }
    
    /**
     * Check if result contains a specific symbol.
     * @param result The slot result
     * @param symbol The symbol to check for
     * @return true if symbol is found
     */
    private boolean containsSymbol(String[] result, String symbol) {
        for (String s : result) {
            if (s.equals(symbol)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * Display the payout table.
     */
    private void showPayoutTable() {
        System.out.println("\n" + repeatString("=", 55));
        System.out.println("*                PAYOUT TABLE                *");
        System.out.println(repeatString("=", 55));
        System.out.println("* Triple <7> <7> <7> (JACKPOT) -> 50x your bet *");
        System.out.println("* Triple BAR BAR BAR           -> 20x your bet *");
        System.out.println("* Triple BEL BEL BEL           -> 15x your bet *");
        System.out.println("* Triple *** *** ***           -> 10x your bet *");
        System.out.println("* Triple CHR CHR CHR           -> 8x your bet  *");
        System.out.println("* Triple LMN LMN LMN           -> 5x your bet  *");
        System.out.println("* Two <7> <7>                  -> 5x your bet  *");
        System.out.println("* Two BAR BAR                  -> 3x your bet  *");
        System.out.println("* Two CHR CHR                  -> 2x your bet  *");
        System.out.println("* Any CHR                      -> 1x your bet  *");
        System.out.println(repeatString("=", 55));
    }
    
    /**
     * Play one round of slots.
     * @param useLastBet Whether to use the previous bet amount
     * @return true to continue playing
     */
    private boolean playRound(boolean useLastBet) {
        // Place bet
        if (!placeBet(useLastBet)) {
            return false;
        }
        
        // Spin the reels
        String[] result = spinReels();
        displaySlotMachine(result);
        
        // Calculate winnings
        int multiplier = calculateWinnings(result);
        int winnings = currentBet * multiplier;
        
        if (multiplier > 0) {
            playerBalance += winnings - currentBet; // Subtract original bet, add winnings
            if (multiplier >= 50) {
                System.out.println("*** JACKPOT!!! You won $" + winnings + "! ***");
            } else if (multiplier >= 10) {
                System.out.println("*** BIG WIN! You won $" + winnings + "! ***");
            } else {
                System.out.println("+ You won $" + winnings + "! +");
            }
        } else {
            playerBalance -= currentBet;
            System.out.println("- Sorry, you lost $" + currentBet + ". Better luck next time!");
        }

        System.out.println("$ Your balance: $" + playerBalance);
        
        // Ask if player wants to continue
        if (playerBalance <= 0) {
            System.out.println("X You're out of money! Thanks for playing!");
            return false;
        }

        while (true) {
            System.out.println("\n" + repeatString("-", 50));
            System.out.println("* What would you like to do next?");
            System.out.println("(s) Spin again with same bet ($" + lastBet + ")");
            System.out.println("(c) Change bet and spin");
            System.out.println("(p) View payout table");
            System.out.println("(q) Quit to casino lobby");
            System.out.println(repeatString("-", 50));
            System.out.print("Your choice: ");

            String choice = scanner.nextLine().toLowerCase().trim();
            if (choice.equals("s") || choice.equals("spin")) {
                return true; // Use last bet
            } else if (choice.equals("c") || choice.equals("change")) {
                if (changeBet()) {
                    return true; // Spin with new bet
                }
                // If changeBet failed, continue the loop
            } else if (choice.equals("p") || choice.equals("payout")) {
                showPayoutTable();
                // Continue the loop to ask again
            } else if (choice.equals("q") || choice.equals("quit")) {
                return false;
            } else {
                System.out.println("- Please enter 's', 'c', 'p', or 'q'.");
            }
        }
    }
    
    /**
     * Helper method to repeat a string.
     * @param str The string to repeat
     * @param count Number of times to repeat
     * @return Repeated string
     */
    private String repeatString(String str, int count) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < count; i++) {
            sb.append(str);
        }
        return sb.toString();
    }
    
    /**
     * Main game loop for slots.
     */
    public void play() {
        System.out.println("*** WELCOME TO THE SLOT MACHINE! ***");
        System.out.println("* Try your luck with our 3-reel slot machine!");
        System.out.println("* Starting balance: $" + playerBalance);
        System.out.println("* Maximum bet per spin: $100");
        System.out.println("* Default bet: $" + lastBet);
        System.out.println(repeatString("=", 55));

        showPayoutTable();

        // First round - ask for bet
        boolean firstRound = true;
        while (playRound(!firstRound)) {
            firstRound = false; // After first round, use last bet by default
        }

        System.out.println("\n*** Thanks for playing slots! Final balance: $" + playerBalance + " ***");
    }
    
    /**
     * Get the current player balance.
     * @return Current balance
     */
    public int getBalance() {
        return playerBalance;
    }
}
