import java.util.Random;
import java.util.Scanner;

/**
 * Slot machine game with 3 reels and various winning combinations.
 */
public class SlotGame {
    private int playerBalance;
    private int currentBet;
    private int lastBet; // Store the last bet for quick replay
    private Scanner scanner;
    private Random random;
    
    // Slot symbols with enhanced ASCII art and better visual representation
    private static final String[] SYMBOLS = {"[CHR]", "[LMN]", "[ORG]", "[GRP]", "[BEL]", "[DIA]", "[STR]", "[SLT]", "[$$$]", "[LCK]"};
    private static final String JACKPOT_SYMBOL = "[$$$]";
    private static final String BONUS_SYMBOL = "[SLT]";
    
    public SlotGame(int startingBalance) {
        this.playerBalance = startingBalance;
        this.currentBet = 0;
        this.lastBet = 10; // Default bet amount
        this.scanner = new Scanner(System.in);
        this.random = new Random();
    }
    
    /**
     * Allow player to place a bet.
     * @param useLastBet Whether to use the last bet amount automatically
     * @return true if bet was placed successfully
     */
    private boolean placeBet(boolean useLastBet) {
        System.out.println("\n$ Your balance: $" + playerBalance + " $");

        if (playerBalance <= 0) {
            System.out.println("X You're out of money! Game over. X");
            return false;
        }

        if (useLastBet && lastBet > 0 && lastBet <= playerBalance) {
            currentBet = lastBet;
            System.out.println("* Using previous bet: $" + currentBet + " *");
            return true;
        }

        while (true) {
            try {
                System.out.print("$ Place your bet (1-" + Math.min(playerBalance, 1000) + "): $");
                int bet = scanner.nextInt();
                scanner.nextLine(); // Consume newline

                if (bet >= 1 && bet <= playerBalance && bet <= 1000) {
                    currentBet = bet;
                    lastBet = bet; // Store for next time
                    System.out.println("+ Bet placed: $" + bet + " +");
                    return true;
                } else if (bet > 1000) {
                    System.out.println("! Maximum bet is $1000 !");
                } else {
                    System.out.println("! Please bet between $1 and $" + Math.min(playerBalance, 1000) + " !");
                }
            } catch (Exception e) {
                System.out.println("- Please enter a valid number. -");
                scanner.nextLine(); // Clear invalid input
            }
        }
    }

    /**
     * Allow player to change their bet amount.
     * @return true if bet was changed successfully
     */
    private boolean changeBet() {
        System.out.println("\n~ CHANGE BET AMOUNT ~");
        System.out.println("$ Current bet: $" + lastBet + " $");
        return placeBet(false); // Force new bet entry
    }
    
    /**
     * Spin the slot machine reels.
     * @return Array of 3 symbols
     */
    private String[] spinReels() {
        String[] result = new String[3];
        for (int i = 0; i < 3; i++) {
            result[i] = SYMBOLS[random.nextInt(SYMBOLS.length)];
        }
        return result;
    }
    
    /**
     * Display the slot machine with spinning animation.
     * @param finalResult The final result to display
     */
    private void displaySlotMachine(String[] finalResult) {
        System.out.println("\n" + repeatString("=", 60));
        System.out.println("***        SLOT MACHINE SPINNING...        ***");
        System.out.println(repeatString("=", 60));
        System.out.println("*** Bet: $" + currentBet + " ***");
        System.out.println(repeatString("-", 60));

        // Enhanced spinning animation with more frames
        String[] spinFrames = {"~", "*", "+", "o"};
        for (int spin = 0; spin < 12; spin++) {
            String[] tempResult = spinReels();
            String frame = spinFrames[spin % spinFrames.length];
            System.out.print("\r*** | " + tempResult[0] + " | " + tempResult[1] + " | " + tempResult[2] + " | *** " + frame);
            try {
                Thread.sleep(120);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }

        // Final result with celebration
        System.out.print("\r*** | " + finalResult[0] + " | " + finalResult[1] + " | " + finalResult[2] + " | *** *");
        System.out.println("\n" + repeatString("=", 60));
    }
    
    /**
     * Calculate winnings based on the slot result.
     * @param result The slot machine result
     * @return Winnings multiplier
     */
    private int calculateWinnings(String[] result) {
        // Check for three of a kind
        if (result[0].equals(result[1]) && result[1].equals(result[2])) {
            if (result[0].equals(JACKPOT_SYMBOL)) {
                return 100; // [$$$] MEGA JACKPOT: 100x bet
            } else if (result[0].equals(BONUS_SYMBOL)) {
                return 75; // [SLT] BONUS JACKPOT: 75x bet
            } else if (result[0].equals("[DIA]")) {
                return 50; // [DIA] Diamond: 50x bet
            } else if (result[0].equals("[BEL]")) {
                return 25; // [BEL] Bell: 25x bet
            } else if (result[0].equals("[STR]")) {
                return 20; // [STR] Star: 20x bet
            } else if (result[0].equals("[LCK]")) {
                return 15; // [LCK] Lucky Clover: 15x bet
            } else if (result[0].equals("[GRP]")) {
                return 12; // [GRP] Grapes: 12x bet
            } else if (result[0].equals("[ORG]")) {
                return 10; // [ORG] Orange: 10x bet
            } else if (result[0].equals("[LMN]")) {
                return 8; // [LMN] Lemon: 8x bet
            } else if (result[0].equals("[CHR]")) {
                return 6; // [CHR] Cherry: 6x bet
            }
        }

        // Check for two of a kind
        if (result[0].equals(result[1]) || result[1].equals(result[2]) || result[0].equals(result[2])) {
            if (containsSymbol(result, JACKPOT_SYMBOL)) {
                return 10; // Two [$$$]: 10x bet
            } else if (containsSymbol(result, BONUS_SYMBOL)) {
                return 8; // Two [SLT]: 8x bet
            } else if (containsSymbol(result, "[DIA]")) {
                return 6; // Two [DIA]: 6x bet
            } else if (containsSymbol(result, "[BEL]")) {
                return 4; // Two [BEL]: 4x bet
            } else if (containsSymbol(result, "[CHR]")) {
                return 3; // Two [CHR]: 3x bet
            } else if (containsSymbol(result, "[LCK]")) {
                return 2; // Two [LCK]: 2x bet
            }
        }

        // Check for any lucky symbols (consolation prizes)
        if (containsSymbol(result, "[CHR]")) {
            return 1; // Any [CHR]: 1x bet (break even)
        }
        if (containsSymbol(result, "[LCK]")) {
            return 1; // Any [LCK]: 1x bet (break even)
        }

        return 0; // No win
    }
    
    /**
     * Check if result contains a specific symbol.
     * @param result The slot result
     * @param symbol The symbol to check for
     * @return true if symbol is found
     */
    private boolean containsSymbol(String[] result, String symbol) {
        for (String s : result) {
            if (s.equals(symbol)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * Display the payout table.
     */
    private void showPayoutTable() {
        System.out.println("\n" + repeatString("=", 65));
        System.out.println("***            $$$ PAYOUT TABLE $$$            ***");
        System.out.println(repeatString("=", 65));
        System.out.println("*** TRIPLE COMBINATIONS (3 matching symbols) ***");
        System.out.println("[$$$] [$$$] [$$$] MEGA JACKPOT!     -> 100x your bet! ***");
        System.out.println("[SLT] [SLT] [SLT] BONUS JACKPOT!    ->  75x your bet! ***");
        System.out.println("[DIA] [DIA] [DIA] Triple Diamond     ->  50x your bet! ***");
        System.out.println("[BEL] [BEL] [BEL] Triple Bell       ->  25x your bet! ***");
        System.out.println("[STR] [STR] [STR] Triple Star       ->  20x your bet! ***");
        System.out.println("[LCK] [LCK] [LCK] Triple Clover     ->  15x your bet! ***");
        System.out.println("[GRP] [GRP] [GRP] Triple Grapes     ->  12x your bet! ***");
        System.out.println("[ORG] [ORG] [ORG] Triple Orange     ->  10x your bet! ***");
        System.out.println("[LMN] [LMN] [LMN] Triple Lemon      ->   8x your bet! ***");
        System.out.println("[CHR] [CHR] [CHR] Triple Cherry     ->   6x your bet! ***");
        System.out.println("");
        System.out.println("*** DOUBLE COMBINATIONS (2 matching symbols) ***");
        System.out.println("[$$$] [$$$] Two Money Bags       ->  10x your bet! ***");
        System.out.println("[SLT] [SLT] Two Slot Machines    ->   8x your bet! ***");
        System.out.println("[DIA] [DIA] Two Diamonds         ->   6x your bet! ***");
        System.out.println("[BEL] [BEL] Two Bells            ->   4x your bet! ***");
        System.out.println("[CHR] [CHR] Two Cherries         ->   3x your bet! ***");
        System.out.println("[LCK] [LCK] Two Clovers          ->   2x your bet! ***");
        System.out.println("");
        System.out.println("*** CONSOLATION PRIZES ***");
        System.out.println("[CHR] Any Cherry              ->   1x your bet! ***");
        System.out.println("[LCK] Any Clover              ->   1x your bet! ***");
        System.out.println(repeatString("=", 65));
    }
    
    /**
     * Play one round of slots.
     * @param useLastBet Whether to use the previous bet amount
     * @return true to continue playing
     */
    private boolean playRound(boolean useLastBet) {
        // Place bet
        if (!placeBet(useLastBet)) {
            return false;
        }
        
        // Spin the reels
        String[] result = spinReels();
        displaySlotMachine(result);
        
        // Calculate winnings
        int multiplier = calculateWinnings(result);
        int winnings = currentBet * multiplier;
        
        if (multiplier > 0) {
            playerBalance += winnings - currentBet; // Subtract original bet, add winnings
            if (multiplier >= 75) {
                System.out.println("*** *** *** MEGA JACKPOT!!! *** *** ***");
                System.out.println("$$$ $$$ $$$ You won $" + winnings + "! $$$ $$$ $$$");
                System.out.println("*** *** *** INCREDIBLE WIN! *** *** ***");
            } else if (multiplier >= 50) {
                System.out.println("*** *** *** SUPER JACKPOT! *** *** ***");
                System.out.println("$$$ $$$ You won $" + winnings + "! $$$ $$$");
            } else if (multiplier >= 20) {
                System.out.println("*** *** BIG WIN! *** ***");
                System.out.println("*** You won $" + winnings + "! ***");
            } else if (multiplier >= 10) {
                System.out.println("*** GREAT WIN! ***");
                System.out.println("+ You won $" + winnings + "! +");
            } else if (multiplier >= 5) {
                System.out.println("** NICE WIN! **");
                System.out.println("$ You won $" + winnings + "! $");
            } else {
                System.out.println("* Lucky! You won $" + winnings + "! *");
            }
        } else {
            playerBalance -= currentBet;
            System.out.println("- Sorry, you lost $" + currentBet + ". Better luck next time! -");
        }

        System.out.println("$ Your balance: $" + playerBalance + " $");

        // Ask if player wants to continue
        if (playerBalance <= 0) {
            System.out.println("X You're out of money! Thanks for playing! X");
            return false;
        }

        while (true) {
            System.out.println("\n" + repeatString("-", 50));
            System.out.println("* What would you like to do next? *");
            System.out.println("* (s) Spin again with same bet ($" + lastBet + ")");
            System.out.println("$ (c) Change bet and spin");
            System.out.println("? (p) View payout table");
            System.out.println("X (q) Quit to casino lobby");
            System.out.println(repeatString("-", 50));
            System.out.print("* Your choice: ");

            String choice = scanner.nextLine().toLowerCase().trim();
            if (choice.equals("s") || choice.equals("spin")) {
                return true; // Use last bet
            } else if (choice.equals("c") || choice.equals("change")) {
                if (changeBet()) {
                    return true; // Spin with new bet
                }
                // If changeBet failed, continue the loop
            } else if (choice.equals("p") || choice.equals("payout")) {
                showPayoutTable();
                // Continue the loop to ask again
            } else if (choice.equals("q") || choice.equals("quit")) {
                return false;
            } else {
                System.out.println("- Please enter 's', 'c', 'p', or 'q'.");
            }
        }
    }
    
    /**
     * Helper method to repeat a string.
     * @param str The string to repeat
     * @param count Number of times to repeat
     * @return Repeated string
     */
    private String repeatString(String str, int count) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < count; i++) {
            sb.append(str);
        }
        return sb.toString();
    }
    
    /**
     * Main game loop for slots.
     */
    public void play() {
        System.out.println("*** *** *** WELCOME TO THE ENHANCED SLOT MACHINE! *** *** ***");
        System.out.println("*** Try your luck with our magical 3-reel slot machine! ***");
        System.out.println("$ Starting balance: $" + playerBalance + " $");
        System.out.println("* Maximum bet per spin: $100 *");
        System.out.println("+ Default bet: $" + lastBet + " +");
        System.out.println(repeatString("=", 65));

        showPayoutTable();

        // First round - ask for bet
        boolean firstRound = true;
        while (playRound(!firstRound)) {
            firstRound = false; // After first round, use last bet by default
        }

        System.out.println("\n*** Thanks for playing the Enhanced Slot Machine! ***");
        System.out.println("$ Final balance: $" + playerBalance + " $");
        if (playerBalance > 1000) {
            System.out.println("*** Congratulations! You're a winner! ***");
        } else if (playerBalance == 1000) {
            System.out.println("+ You broke even! Not bad! +");
        } else {
            System.out.println("* Better luck next time! *");
        }
    }
    
    /**
     * Get the current player balance.
     * @return Current balance
     */
    public int getBalance() {
        return playerBalance;
    }
}
