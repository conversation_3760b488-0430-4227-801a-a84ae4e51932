import java.util.*;

/**
 * Represents a hand of cards in blackjack.
 */
public class Hand {
    private List<Card> cards;
    
    public Hand() {
        this.cards = new ArrayList<>();
    }
    
    /**
     * Add a card to the hand.
     * @param card The card to add
     */
    public void addCard(Card card) {
        cards.add(card);
    }
    
    /**
     * Calculate the best possible value of the hand.
     * @return The optimal hand value
     */
    public int getValue() {
        int value = 0;
        int aces = 0;
        
        for (Card card : cards) {
            if (card.getRank().equals("Ace")) {
                aces++;
                value += 11;
            } else {
                value += card.getValue();
            }
        }
        
        // Adjust for aces
        while (value > 21 && aces > 0) {
            value -= 10;
            aces--;
        }
        
        return value;
    }
    
    /**
     * Check if hand is a blackjack (21 with 2 cards).
     * @return true if hand is blackjack
     */
    public boolean isBlackjack() {
        return cards.size() == 2 && getValue() == 21;
    }
    
    /**
     * Check if hand is bust (over 21).
     * @return true if hand is bust
     */
    public boolean isBust() {
        return getValue() > 21;
    }
    
    /**
     * Get the cards in the hand.
     * @return List of cards
     */
    public List<Card> getCards() {
        return new ArrayList<>(cards);
    }
    
    /**
     * Get the number of cards in the hand.
     * @return Number of cards
     */
    public int size() {
        return cards.size();
    }
    
    /**
     * Clear all cards from the hand.
     */
    public void clear() {
        cards.clear();
    }
    
    /**
     * Get the last card added to the hand.
     * @return The last card, or null if hand is empty
     */
    public Card getLastCard() {
        if (cards.isEmpty()) {
            return null;
        }
        return cards.get(cards.size() - 1);
    }
    
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < cards.size(); i++) {
            if (i > 0) {
                sb.append(", ");
            }
            sb.append(cards.get(i).toString());
        }
        sb.append(" (Value: ").append(getValue()).append(")");
        return sb.toString();
    }
}
