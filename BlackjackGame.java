import java.util.Scanner;

/**
 * Main blackjack game class with complete game logic.
 */
public class BlackjackGame {
    private Deck deck;
    private Hand playerHand;
    private Hand dealerHand;
    private int playerBalance;
    private int currentBet;
    private Scanner scanner;

    // For splitting functionality
    private Hand splitHand;
    private boolean isSplitGame;
    private boolean playingFirstHand;
    
    public BlackjackGame() {
        this.deck = new Deck();
        this.playerHand = new Hand();
        this.dealerHand = new Hand();
        this.splitHand = new Hand();
        this.playerBalance = 1000;
        this.currentBet = 0;
        this.scanner = new Scanner(System.in);
        this.isSplitGame = false;
        this.playingFirstHand = true;
    }

    /**
     * Set the player's balance (for casino integration).
     * @param balance The new balance
     */
    public void setBalance(int balance) {
        this.playerBalance = balance;
    }

    /**
     * Get the player's current balance (for casino integration).
     * @return Current balance
     */
    public int getBalance() {
        return this.playerBalance;
    }
    
    /**
     * Reset hands for a new game.
     */
    private void resetHands() {
        playerHand.clear();
        dealerHand.clear();
        splitHand.clear();
        isSplitGame = false;
        playingFirstHand = true;
    }
    
    /**
     * Allow player to place a bet.
     * @return true if bet was placed successfully
     */
    private boolean placeBet() {
        System.out.println("\n$ Your balance: $" + playerBalance);

        if (playerBalance <= 0) {
            System.out.println("X You're out of money! Game over.");
            return false;
        }

        while (true) {
            try {
                System.out.print("$ Place your bet (1-" + playerBalance + ") or 'a' for ALL IN: $");
                String input = scanner.nextLine().trim();

                if (input.toLowerCase().equals("a") || input.toLowerCase().equals("all")) {
                    currentBet = playerBalance;
                    System.out.println("*** ALL IN! Betting everything: $" + currentBet + " ***");
                    return true;
                }

                int bet = Integer.parseInt(input);

                if (bet >= 1 && bet <= playerBalance) {
                    currentBet = bet;
                    System.out.println("+ Bet placed: $" + bet);
                    return true;
                } else {
                    System.out.println("- Please bet between $1 and $" + playerBalance);
                }
            } catch (NumberFormatException e) {
                System.out.println("- Please enter a valid number or 'a' for all in.");
            } catch (Exception e) {
                System.out.println("- Please enter a valid number or 'a' for all in.");
            }
        }
    }
    
    /**
     * Deal initial two cards to player and dealer.
     */
    private void dealInitialCards() {
        // Deal two cards to player
        playerHand.addCard(deck.dealCard());
        playerHand.addCard(deck.dealCard());
        
        // Deal two cards to dealer
        dealerHand.addCard(deck.dealCard());
        dealerHand.addCard(deck.dealCard());
    }
    
    /**
     * Display current hands with enhanced formatting.
     * @param hideDealerCard whether to hide dealer's second card
     */
    private void showHands(boolean hideDealerCard) {
        System.out.println("\n" + repeatString("=", 60));

        if (isSplitGame) {
            System.out.println("* HAND 1: " + formatHand(playerHand) +
                             (playingFirstHand ? " <- CURRENT" : ""));
            System.out.println("* HAND 2: " + formatHand(splitHand) +
                             (!playingFirstHand ? " <- CURRENT" : ""));
        } else {
            System.out.println("* YOUR HAND: " + formatHand(playerHand));
        }

        if (hideDealerCard && dealerHand.size() >= 2) {
            System.out.println("* DEALER'S HAND: " + dealerHand.getCards().get(0) + ", [HIDDEN]");
        } else {
            System.out.println("* DEALER'S HAND: " + formatHand(dealerHand));
        }
        System.out.println(repeatString("=", 60));
    }

    /**
     * Format a hand for display with cards and value.
     * @param hand The hand to format
     * @return Formatted string representation
     */
    private String formatHand(Hand hand) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < hand.getCards().size(); i++) {
            if (i > 0) {
                sb.append(" ");
            }
            sb.append(hand.getCards().get(i).toString());
        }
        sb.append(" (Value: ").append(hand.getValue()).append(")");
        return sb.toString();
    }

    /**
     * Helper method to repeat a string (for Java versions before 11).
     * @param str The string to repeat
     * @param count Number of times to repeat
     * @return Repeated string
     */
    private String repeatString(String str, int count) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < count; i++) {
            sb.append(str);
        }
        return sb.toString();
    }

    /**
     * Check if player can double down.
     * @param hand The hand to check
     * @return true if doubling down is allowed
     */
    private boolean canDoubleDown(Hand hand) {
        return hand.size() == 2 && playerBalance >= currentBet;
    }

    /**
     * Check if player can split their hand.
     * @return true if splitting is allowed
     */
    private boolean canSplit() {
        if (playerHand.size() != 2 || playerBalance < currentBet || isSplitGame) {
            return false;
        }

        // Check if both cards have the same rank
        Card card1 = playerHand.getCards().get(0);
        Card card2 = playerHand.getCards().get(1);
        return card1.getRank().equals(card2.getRank());
    }

    /**
     * Handle doubling down action.
     * @param hand The hand to double down on
     * @return true if the action was successful
     */
    private boolean doubleDown(Hand hand) {
        if (!canDoubleDown(hand)) {
            return false;
        }

        // Double the bet
        playerBalance -= currentBet;
        currentBet *= 2;

        // Deal exactly one more card
        Card newCard = deck.dealCard();
        hand.addCard(newCard);

        System.out.println("*** DOUBLED DOWN! ***");
        System.out.println("+ New bet: $" + currentBet);
        System.out.println("+ You drew: " + newCard);

        return true;
    }

    /**
     * Handle splitting action.
     * @return true if the action was successful
     */
    private boolean splitHands() {
        if (!canSplit()) {
            return false;
        }

        // Move second card to split hand
        Card secondCard = playerHand.getCards().get(1);
        playerHand.getCards().remove(1);
        splitHand.addCard(secondCard);

        // Deal new cards to both hands
        playerHand.addCard(deck.dealCard());
        splitHand.addCard(deck.dealCard());

        // Set split game flags
        isSplitGame = true;
        playingFirstHand = true;

        // Deduct additional bet for split hand
        playerBalance -= currentBet;

        System.out.println("*** HANDS SPLIT! ***");
        System.out.println("+ Additional bet placed: $" + currentBet);
        System.out.println("+ Total bet: $" + (currentBet * 2));

        return true;
    }
    
    /**
     * Handle player's turn for a specific hand.
     * @param hand The hand being played
     * @param isFirstTurn Whether this is the first turn (for special actions)
     * @return true if player didn't bust
     */
    private boolean playHand(Hand hand, boolean isFirstTurn) {

        while (true) {
            showHands(true);

            if (hand.isBust()) {
                if (isSplitGame) {
                    System.out.println("\nX Hand " + (playingFirstHand ? "1" : "2") + " busted!");
                } else {
                    System.out.println("\nX You busted! You lose.");
                }
                return false;
            }

            if (hand.getValue() == 21) {
                if (isSplitGame) {
                    System.out.println("\n* Perfect! Hand " + (playingFirstHand ? "1" : "2") + " has 21!");
                } else {
                    System.out.println("\n* Perfect! You have 21!");
                }
                break;
            }

            // Show available actions
            StringBuilder prompt = new StringBuilder("\nChoose action: (h)it, (s)tand");

            if (isFirstTurn && canDoubleDown(hand)) {
                prompt.append(", (d)ouble down");
            }

            if (isFirstTurn && canSplit() && !isSplitGame) {
                prompt.append(", s(p)lit");
            }

            prompt.append(": ");
            System.out.print(prompt.toString());

            String action = scanner.nextLine().toLowerCase().trim();

            if (action.equals("h") || action.equals("hit")) {
                Card newCard = deck.dealCard();
                hand.addCard(newCard);
                System.out.println("+ You drew: " + newCard);
                isFirstTurn = false; // Can't double down or split after hitting

            } else if (action.equals("s") || action.equals("stand")) {
                break;

            } else if ((action.equals("d") || action.equals("double")) && isFirstTurn && canDoubleDown(hand)) {
                if (doubleDown(hand)) {
                    break; // Must stand after doubling down
                }

            } else if ((action.equals("p") || action.equals("split")) && isFirstTurn && canSplit() && !isSplitGame) {
                return handleSplitGame();

            } else {
                System.out.println("Invalid action. Please try again.");
            }
        }

        return !hand.isBust();
    }

    /**
     * Handle the complete split game scenario.
     * @return true if at least one hand didn't bust
     */
    private boolean handleSplitGame() {
        if (!splitHands()) {
            return false;
        }

        // Play first hand
        System.out.println("\n*** PLAYING HAND 1 ***");
        playingFirstHand = true;
        boolean firstHandResult = playHand(playerHand, true);

        // Play second hand
        System.out.println("\n*** PLAYING HAND 2 ***");
        playingFirstHand = false;
        boolean secondHandResult = playHand(splitHand, true);

        return firstHandResult || secondHandResult;
    }

    /**
     * Handle player's turn (wrapper for backward compatibility).
     * @return true if player didn't bust
     */
    private boolean playerTurn() {
        if (isSplitGame) {
            return handleSplitGame();
        } else {
            return playHand(playerHand, true);
        }
    }
    
    /**
     * Handle dealer's turn.
     */
    private void dealerTurn() {
        System.out.println("\n* DEALER'S TURN:");
        showHands(false);

        while (dealerHand.getValue() < 17) {
            Card card = deck.dealCard();
            dealerHand.addCard(card);
            System.out.println("* Dealer draws: " + card);
            System.out.println("* Dealer's hand: " + formatHand(dealerHand));
        }

        if (dealerHand.isBust()) {
            System.out.println("X Dealer busted!");
        }
    }
    
    /**
     * Determine the winner for a specific hand and return result.
     * @param hand The hand to evaluate
     * @return String representing the game result
     */
    private String determineWinner(Hand hand) {
        int playerValue = hand.getValue();
        int dealerValue = dealerHand.getValue();

        // Check for blackjacks (only possible if not split)
        boolean playerBlackjack = hand.isBlackjack() && !isSplitGame;
        boolean dealerBlackjack = dealerHand.isBlackjack();

        if (playerBlackjack && dealerBlackjack) {
            return "push_blackjack";
        } else if (playerBlackjack) {
            return "player_blackjack";
        } else if (dealerBlackjack) {
            return "dealer_blackjack";
        }

        // Check for busts
        if (hand.isBust()) {
            return "player_bust";
        } else if (dealerHand.isBust()) {
            return "dealer_bust";
        }

        // Compare values
        if (playerValue > dealerValue) {
            return "player_wins";
        } else if (dealerValue > playerValue) {
            return "dealer_wins";
        } else {
            return "push";
        }
    }

    /**
     * Determine the winner and return result (backward compatibility).
     * @return String representing the game result
     */
    private String determineWinner() {
        return determineWinner(playerHand);
    }
    
    /**
     * Handle betting payouts based on game result for a specific hand.
     * @param result The game result
     * @param handNumber The hand number (1 or 2 for split, 0 for regular)
     */
    private void handlePayout(String result, int handNumber) {
        String handLabel = handNumber > 0 ? "Hand " + handNumber + ": " : "";

        switch (result) {
            case "player_blackjack":
                int payout = (int) (currentBet * 1.5); // 3:2 payout for blackjack
                playerBalance += payout;
                System.out.println("*** " + handLabel + "BLACKJACK! You win $" + payout + "! ***");
                break;
            case "player_wins":
            case "dealer_bust":
                playerBalance += currentBet;
                System.out.println("*** " + handLabel + "You win $" + currentBet + "! ***");
                break;
            case "push":
            case "push_blackjack":
                System.out.println("= " + handLabel + "It's a push! Your bet is returned.");
                break;
            default: // Player loses
                playerBalance -= currentBet;
                System.out.println("- " + handLabel + "You lose $" + currentBet + ".");
                break;
        }
    }

    /**
     * Handle betting payouts based on game result (backward compatibility).
     * @param result The game result
     */
    private void handlePayout(String result) {
        handlePayout(result, 0);
    }

    /**
     * Handle payouts for split hands.
     */
    private void handleSplitPayouts() {
        System.out.println("\n*** SPLIT GAME RESULTS ***");

        // Handle first hand
        String result1 = determineWinner(playerHand);
        handlePayout(result1, 1);

        // Handle second hand
        String result2 = determineWinner(splitHand);
        handlePayout(result2, 2);

        System.out.println("*** END SPLIT RESULTS ***");
    }
    
    /**
     * Play one round of blackjack.
     * @return true to continue playing
     */
    private boolean playRound() {
        resetHands();
        
        // Place bet
        if (!placeBet()) {
            return false;
        }
        
        // Deal initial cards
        dealInitialCards();
        
        // Check for initial blackjacks
        if (playerHand.isBlackjack() || dealerHand.isBlackjack()) {
            showHands(false);
            if (isSplitGame) {
                handleSplitPayouts();
            } else {
                String result = determineWinner();
                handlePayout(result);
            }
        } else {
            // Player's turn
            if (playerTurn()) {
                // Dealer's turn (only if player didn't bust)
                dealerTurn();
            }

            // Determine winner and handle payout
            if (isSplitGame) {
                handleSplitPayouts();
            } else {
                String result = determineWinner();
                handlePayout(result);
            }
        }
        
        System.out.println("Your balance: $" + playerBalance);
        
        // Ask if player wants to continue
        if (playerBalance <= 0) {
            System.out.println("You're out of money! Thanks for playing!");
            return false;
        }
        
        while (true) {
            System.out.print("\nDo you want to play another round? (y/n): ");
            String playAgain = scanner.nextLine().toLowerCase().trim();
            if (playAgain.equals("y") || playAgain.equals("yes")) {
                return true;
            } else if (playAgain.equals("n") || playAgain.equals("no")) {
                return false;
            } else {
                System.out.println("Please enter 'y' for yes or 'n' for no.");
            }
        }
    }
    
    /**
     * Main game loop.
     */
    public void play() {
        System.out.println("*** WELCOME TO ENHANCED BLACKJACK! ***");
        System.out.println("* Try to get as close to 21 as possible without going over.");
        System.out.println("* Face cards (J, Q, K) are worth 10, Aces are worth 1 or 11.");
        System.out.println("* Blackjack (21 with 2 cards) pays 3:2!");
        System.out.println("* Starting balance: $" + playerBalance);
        System.out.println("* Card suits: H=Hearts, D=Diamonds, C=Clubs, S=Spades");
        System.out.println("* Type 'a' or 'all' when betting to go ALL IN!");
        System.out.println("* NEW FEATURES:");
        System.out.println("  - DOUBLE DOWN: Double your bet and get one more card");
        System.out.println("  - SPLIT: Split pairs into two separate hands");
        System.out.println(repeatString("=", 65));

        while (playRound()) {
            // Continue playing rounds
        }

        System.out.println("\n*** Thanks for playing! Final balance: $" + playerBalance + " ***");
        // Don't close scanner here - it's managed by the casino
    }
    
    /**
     * Main method to start the game.
     * @param args Command line arguments (not used)
     */
    public static void main(String[] args) {
        BlackjackGame game = new BlackjackGame();
        game.play();
    }
}
