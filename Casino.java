import java.util.Scanner;

/**
 * Main casino class that allows players to choose between different games.
 */
public class Casino {
    private int playerBalance;
    private Scanner scanner;
    
    public Casino() {
        this.playerBalance = 10000; // Starting balance
        this.scanner = new Scanner(System.in);
    }
    
    /**
     * Display the main casino welcome screen.
     */
    private void displayWelcome() {
        System.out.println(repeatString("=", 60));
        System.out.println("*                                                          *");
        System.out.println("*              WELCOME TO JAVA CASINO!                    *");
        System.out.println("*                                                          *");
        System.out.println("*              Your Premier Gaming Destination            *");
        System.out.println("*                                                          *");
        System.out.println(repeatString("=", 60));
        System.out.println("* Starting Balance: $" + playerBalance + "                               *");
        System.out.println("* Card Colors: Hearts/Diamonds=RED, Spades/Clubs=BLUE    *");
        System.out.println(repeatString("=", 60));
    }
    
    /**
     * Display the game selection menu.
     */
    private void displayGameMenu() {
        System.out.println("\n" + repeatString("-", 40));
        System.out.println("*           GAME SELECTION MENU          *");
        System.out.println(repeatString("-", 40));
        System.out.println("* 1. BLACKJACK - Classic card game       *");
        System.out.println("*    Try to get 21 without busting!      *");
        System.out.println("*    Blackjack pays 3:2                  *");
        System.out.println("*                                         *");
        System.out.println("* 2. SLOT MACHINE - Spin to win!         *");
        System.out.println("*    3-reel slots with jackpots!         *");
        System.out.println("*    Jackpot pays 50:1                   *");
        System.out.println("*                                         *");
        System.out.println("* 3. CHECK BALANCE - View your money     *");
        System.out.println("*                                         *");
        System.out.println("* 4. EXIT CASINO - Cash out and leave    *");
        System.out.println(repeatString("-", 40));
    }
    
    /**
     * Get the player's game choice.
     * @return The selected game number
     */
    private int getGameChoice() {
        while (true) {
            try {
                System.out.print("Select a game (1-4): ");
                int choice = scanner.nextInt();
                scanner.nextLine(); // Consume newline
                
                if (choice >= 1 && choice <= 4) {
                    return choice;
                } else {
                    System.out.println("Please select a number between 1 and 4.");
                }
            } catch (Exception e) {
                System.out.println("Please enter a valid number.");
                scanner.nextLine(); // Clear invalid input
            }
        }
    }
    
    /**
     * Play blackjack game.
     */
    private void playBlackjack() {
        System.out.println("\n" + repeatString("*", 50));
        System.out.println("*           ENTERING BLACKJACK TABLE...           *");
        System.out.println(repeatString("*", 50));
        
        // Create blackjack game with current balance
        BlackjackGame blackjack = new BlackjackGame();
        blackjack.setBalance(playerBalance); // We need to add this method
        blackjack.play();
        
        // Update balance after blackjack
        playerBalance = blackjack.getBalance(); // We need to add this method
        
        System.out.println("\n" + repeatString("*", 50));
        System.out.println("*           RETURNING TO CASINO LOBBY...          *");
        System.out.println(repeatString("*", 50));
    }
    
    /**
     * Play slot machine game.
     */
    private void playSlots() {
        System.out.println("\n" + repeatString("*", 50));
        System.out.println("*           APPROACHING SLOT MACHINE...           *");
        System.out.println(repeatString("*", 50));
        
        // Create slot game with current balance
        SlotGame slots = new SlotGame(playerBalance);
        slots.play();
        
        // Update balance after slots
        playerBalance = slots.getBalance();
        
        System.out.println("\n" + repeatString("*", 50));
        System.out.println("*           RETURNING TO CASINO LOBBY...          *");
        System.out.println(repeatString("*", 50));
    }
    
    /**
     * Display current balance.
     */
    private void checkBalance() {
        System.out.println("\n" + repeatString("-", 30));
        System.out.println("*        ACCOUNT BALANCE        *");
        System.out.println(repeatString("-", 30));
        System.out.println("* Current Balance: $" + playerBalance + "         *");
        
        if (playerBalance >= 2000) {
            System.out.println("* Status: HIGH ROLLER!          *");
        } else if (playerBalance >= 1000) {
            System.out.println("* Status: Doing Great!          *");
        } else if (playerBalance >= 500) {
            System.out.println("* Status: Steady Player         *");
        } else if (playerBalance > 0) {
            System.out.println("* Status: Keep Trying!          *");
        } else {
            System.out.println("* Status: BROKE - Game Over!    *");
        }
        
        System.out.println(repeatString("-", 30));
    }
    
    /**
     * Helper method to repeat a string.
     * @param str The string to repeat
     * @param count Number of times to repeat
     * @return Repeated string
     */
    private String repeatString(String str, int count) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < count; i++) {
            sb.append(str);
        }
        return sb.toString();
    }
    
    /**
     * Main casino game loop.
     */
    public void run() {
        displayWelcome();
        
        while (true) {
            // Check if player is broke
            if (playerBalance <= 0) {
                System.out.println("\n" + repeatString("!", 50));
                System.out.println("* You're out of money! Thanks for visiting!       *");
                System.out.println("* Better luck next time!                          *");
                System.out.println(repeatString("!", 50));
                break;
            }
            
            displayGameMenu();
            int choice = getGameChoice();
            
            switch (choice) {
                case 1:
                    playBlackjack();
                    break;
                case 2:
                    playSlots();
                    break;
                case 3:
                    checkBalance();
                    break;
                case 4:
                    System.out.println("\n" + repeatString("*", 50));
                    System.out.println("* Thanks for visiting Java Casino!                *");
                    System.out.println("* You're leaving with $" + playerBalance + "                        *");
                    if (playerBalance > 1000) {
                        System.out.println("* Congratulations on your winnings!               *");
                    } else if (playerBalance == 1000) {
                        System.out.println("* You broke even - not bad!                       *");
                    } else {
                        System.out.println("* Better luck next time!                          *");
                    }
                    System.out.println("* Come back soon!                                  *");
                    System.out.println(repeatString("*", 50));
                    scanner.close();
                    return;
                default:
                    System.out.println("Invalid choice. Please try again.");
            }
        }
        
        scanner.close();
    }
    
    /**
     * Main method to start the casino.
     * @param args Command line arguments (not used)
     */
    public static void main(String[] args) {
        Casino casino = new Casino();
        casino.run();
    }
}
